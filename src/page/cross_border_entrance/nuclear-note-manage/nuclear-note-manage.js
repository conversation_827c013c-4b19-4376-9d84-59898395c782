import React from "react";
import "../../shop-good.less";
import { Button, Modal, Input, Select, Form, message, Tabs, Alert, Table, Tooltip, Space } from "antd";
import NewModal from "../../../components/NewModal";
import TextArea from "antd/lib/input/TextArea";
import { ExclamationCircleOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import { lib, getConfigDataUtils, HOC, event } from "react-single-app";
import axios from "axios";
import MergeTdSearchList from "../../../components/MergeTdSearchList";
import BatchManualProcessingModal from "../../../components/batch-manual-processing-modal";
import { nuclearNoteManageBatchManualData } from "./view-config";

const FormItem = Form.Item;
const { TabPane } = Tabs;

const OrderStatus = {
    INIT: "INIT", // "已创建"
    DECALRING: "DECALRING", //"申报中"
    STORAGING: "STORAGING", //"暂存中"
    STORAGED: "STORAGED", //"已暂存"
    EXAMINE: "EXAMINE", //"已审核"
    EXCEPTION: "EXCEPTION", //"异常"
    STORAGE_EXCEPTION: "STORAGE_EXCEPTION", //"暂存异常"
    DISCARD: "DISCARD", //"已作废"
    DELETED: "DELETED", //"已删除"
    FINISH: "FINISH", //"已完成"
};

@HOC.mapAuthButtonsToState({})
class App extends MergeTdSearchList {
    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(371)).then(res => res.data.data);
    }

    constructor(props) {
        super(props);
        this.state.modalTitle = "新增核注清单";
        // 编辑/新建url
        this.state.upUrl = "/ccs/endorsement/addMailNo";
        this.state.mailNo = "";
        this.state.editRow = {};
        this.state.modalPagination = { current: 1, pageSize: 100 };
        this.state.configList = [
            {
                type: "SELECT",
                labelName: "清单业务",
                labelKey: "bussinessType",
                list: [],
                ccs: "/ccs/endorsement/list-bussiness-types",
                onChange: (e, form) => {
                    if (e === "SECONDE_OUT") {
                        // this.state.editRow.bussinessType2 = "1"
                        form.setFieldsValue({ bussinessType2: "1" });
                        this.state.configList[2].hide = false;
                        this.state.configList[3].hide = true;
                        this.state.configList[4].hide = true;
                    } else {
                        // this.state.editRow.bussinessType2 = "2"
                        form.setFieldsValue({ bussinessType2: "2" });
                        this.state.configList[2].hide = true;
                        this.state.configList[3].hide = false;
                        this.state.configList[4].hide = false;
                        this.state.configList[3].list = this.state.configList[3].lists.filter(
                            item => item.id && item.id.indexOf(e) !== -1,
                        );
                    }
                    this.setState(this.state);
                },
                required: true,
            },
            {
                type: "SELECT",
                labelName: "单据类型",
                labelKey: "bussinessType2",
                list: [
                    { name: "申报出库单", value: "1" },
                    { name: "清关单", value: "2" },
                ],
                disabled: true,
            },
            {
                type: "SELECT",
                labelName: "单据编号",
                labelKey: "exportOrderId",
                list: [],
                ccs: "/ccs/exportOrder/listForEndorsement",
                hide: false,
                required: true,
            },
            {
                type: "SELECT",
                labelName: "单据编号",
                labelKey: "inventoryOrderId",
                list: [],
                ccs: "/ccs/invenorder/list-select-invertory-order-auth",
                hide: true,
                required: true,
            },
            {
                type: "SELECT",
                labelName: "是否核扣账册",
                labelKey: "stockChangeEnable",
                list: [
                    { id: "1", name: "是" },
                    { id: "0", name: "否" },
                ],
                allowClear: true,
            },
        ];
        this.state.eliminateData = {};
        this.state.mergeKeyList = ["orderNo"];
        this.state.mergeDataListKey = "checklistOrderNo";
        this.state.importModalVisible = false;
        this.onSearchReset = this.onSearchReset.bind(this);
        this.tagSelectRef = React.createRef();
    }
    componentWillUnmount() {
        event.off("onSearchReset", this.onSearchReset);
    }
    onSearchReset() {
        this.setState({
            status: "",
        });
    }
    getStatusCount() {
        lib.request({
            url: "/ccs/endorsement/countPagingStatus",
            data: {
                ...this.state.search,
            },
            success: data => {
                const statusCount = {};
                data.map(item => {
                    statusCount[item.status] = item.count;
                });
                this.setState({
                    statusCount,
                    statusList: data,
                });
            },
        });
    }

    statusFn(row) {
        return (
            <>
                {row.generateDeclareStatusDesc}
                {row.generateDeclareReason && (
                    <Tooltip title={row.generateDeclareReason}>
                        <QuestionCircleOutlined />
                    </Tooltip>
                )}
            </>
        );
    }

    renderOperationTopView() {
        const statusCount = this.state.statusCount || {};
        const statusList = this.state.statusList || [];
        return (
            <Tabs
                className="customs-clearance-manage-tabs"
                defaultActiveKey={""}
                activeKey={this.state.status}
                onChange={key => {
                    this.changeImmutable({
                        status: key,
                    });
                    this.setState({
                        status: key,
                    });
                }}>
                <TabPane tab={`全部`} key={""} />
                {/* <TabPane tab={`已创建(${statusCount["CREATED"]})`} key={"CREATED"} />
                <TabPane tab={`申报中(${statusCount["CREATED"]})`} key={"CREATED"} />
                <TabPane tab={`暂存中(${statusCount["ERFECT"]})`} key={"ERFECT"} />
                <TabPane tab={`已暂存(${statusCount["CONFIRMING"]})`} key={"CONFIRMING"} />
                <TabPane tab={`核注异常(${statusCount["ENDORSEMENT"]})`} key={"ENDORSEMENT"} />
                <TabPane
                    tab={`暂存异常(${statusCount["START_STORAGED,START_STORAGING"]})`}
                    key={"START_STORAGED,START_STORAGING"}
                />
                <TabPane tab={`已审核(${statusCount["AUDITED"]})`} key={"AUDITED"} />
                <TabPane tab={`清关完成(${statusCount["COMPLETE"]})`} key={"COMPLETE"} />
                <TabPane tab={`已作废(${statusCount["SERVERING"]})`} key={"SERVERING"} />
                <TabPane tab={`已删除`} key={"FAILURE"} /> */}
                {statusList.map((item, index) => (
                    <TabPane tab={`${item.statusDesc}(${item.count})`} key={item.status} />
                ))}
            </Tabs>
        );
    }

    componentDidMount() {
        this.getConfigList();
        event.on("onSearchReset", this.onSearchReset);
    }

    load(_, toTop) {
        super.load(_, toTop);
        this.getStatusCount();
    }

    getConfigList() {
        let { configList } = this.state;
        configList.map(item => {
            if (item.ccs) {
                lib.request({
                    url: item.ccs,
                    needMask: false,
                    success: res => {
                        item.lists = res;
                        item.list = res;
                        // console.log(res, "res");
                        this.setState({
                            configList,
                        });
                    },
                });
            }
        });
        this.setState({
            configList,
        });
    }

    // renderStatus(row) {
    //     return <Tooltip title={row.informationDesc}>
    //         {row.customsStatusDesc}
    //     </Tooltip>
    // }

    renderCustomsStatusDesc(row) {
        return <Tooltip title={row.informationDesc}>{row.customsStatusDesc}</Tooltip>;
    }

    renderSn(row) {
        return (
            <a
                onClick={() => {
                    lib.openPage(`/ccs/nuclear-note-detail?page_title=核注单详情&id=${row.id}`);
                }}>
                {row.sn}
            </a>
        );
    }

    fetchDetailAndHandle(row, types) {
        lib.request({
            url: "/ccs/endorsement/load-detail-info",
            data: {
                endorsementId: row.id,
            },
            needMask: true,
            success: res => {
                let endorsementDetailWarp = res;
                let defaultValue = "",
                    type = "",
                    defaultStr = "";
                if (endorsementDetailWarp) {
                    // 一线入境展示报关单
                    if (endorsementDetailWarp.bussinessType === "ONELINE_IN") {
                        if (endorsementDetailWarp.listInventoryOrderRelationDTO) {
                            endorsementDetailWarp.listInventoryOrderRelationDTO.map(item => {
                                defaultValue += `${item.relNo}\n`;
                            });
                        }
                        type = "1";
                        // 二线出区展示运单号
                    } else if (endorsementDetailWarp.bussinessType === "SECONDE_OUT") {
                        type = "2";
                        endorsementDetailWarp.itemList.map(item => {
                            defaultValue += `${item.mailNo}\n`;
                        });
                        // 退货入区展示关联单证号
                    } else if (endorsementDetailWarp.bussinessType === "REFUND_INAREA") {
                        type = "3";
                        if (endorsementDetailWarp.listInventoryOrderRelationDTO) {
                            endorsementDetailWarp.listInventoryOrderRelationDTO.map(item => {
                                defaultValue += `${item.relNo}\n`;
                            });
                        }
                    }
                    // endorsementDetailWarp.bodyItemInfos.map(item => {
                    //     let str = `${item.accountSeqNo} ${item.productId}`
                    //     if (defaultStr.indexOf(str) === -1) {
                    //         defaultStr += str + "\n"
                    //     }
                    // })
                }
                row.defaultValue = defaultValue;
                row.type = type;
                row.bodyItemInfos = res.bodyItemInfos.map((item, index) => {
                    item.index = index + 1;
                    return item;
                });
                row.bodyList = res.bodyItemInfos;
                // row.defaultStr = defaultStr;
                this.setState({
                    editRow: row,
                    detailVisible: true,
                    type: types,
                    mailNo: defaultValue,
                    exception: "all",
                });
            },
        });
    }

    export(row) {
        let { pagination, searchConditions } = this.state;
        lib.request({
            url: "/ccs/endorsement/singleExport",
            needMask: true,
            data: { id: row.id },
            success: json => {
                Modal.confirm({
                    okText: "去下载中心",
                    cancelText: "取消",
                    icon: null,
                    content: "新建下载任务成功",
                    onOk() {
                        lib.openPage("/download-center?page_title=下载中心");
                    },
                });
            },
        });
    }

    myOperation(row) {
        return (
            <Space>
                {row.allowFinish && (
                    <a className="link" onClick={e => this.finishFunc(e, row)}>
                        手动审核
                    </a>
                )}
                {/* {row.allowExport && <a className="link" onClick={(e) => {lib.download('/ccs/endorsement/singleExport' , {id: row.id})}}>导出</a>} */}
                {row.allowExport && (
                    <a
                        className="link"
                        onClick={e => {
                            this.export(row);
                        }}>
                        导出
                    </a>
                )}
                {row.allowDiscard && (
                    <a className="link" onClick={() => this.invalidRow(row)}>
                        作废
                    </a>
                )}
                {row.allowEdit && (
                    <a className="link" onClick={() => this.fetchDetailAndHandle(row, "edit")}>
                        编辑
                    </a>
                )}
                {row.allowView && (
                    <a className="link" onClick={() => this.fetchDetailAndHandle(row, "watch")}>
                        {/* // <a className="link" onClick={() => lib.openPage(`/ccs/nuclear-note-detail?page_title=核注单详情&id=${row.id}`)}> */}
                        查看
                    </a>
                )}
                {/* {(row.statusDesc === '已创建' || row.statusDesc === '异常' || (row.statusDesc === '申报中' && row.realEndorsementOrderNo === '')) && <a className="link" onClick={() => this.staging(row)}>暂存</a>} */}
                {/* {row.allow} */}
                {[
                    OrderStatus.INIT,
                    OrderStatus.STORAGED,
                    OrderStatus.EXCEPTION,
                    OrderStatus.STORAGE_EXCEPTION,
                ].includes(row.status) && (
                        <span
                            className="link"
                            onClick={() => {
                                this.setState({
                                    remackModal: true,
                                    editRow: row,
                                });
                            }}>
                            备注
                        </span>
                    )}

                {row.allowStorage && (
                    <span className="link" onClick={() => this.staging(row)}>
                        暂存
                    </span>
                )}
                {row.allowEliminateException && (
                    <span className="link" onClick={() => this.eliminate(row)}>
                        剔除异常
                    </span>
                )}
                {row.allowDeletedApply && (
                    <span className="link" onClick={() => this.deletedApply(row)}>
                        手动删除申请
                    </span>
                )}
            </Space>
        );
    }

    // 剔除异常
    eliminate(row) {
        lib.request({
            url: "/ccs/endorsement/eliminateException",
            data: {
                id: row.id,
            },
            needMask: true,
            success: res => {
                message.success("剔除异常成功");
                let exceptionItem = [];
                Object.entries(res.exceptionItem).map(([key, value]) => {
                    exceptionItem.push({ key, value });
                });
                this.setState({
                    eliminateModalVisible: true,
                    eliminateData: {
                        exceptionMailNos: res.exceptionMailNos || [],
                        exceptionItem,
                    },
                });
                this.load(true);
            },
        });
    }

    // 暂存
    staging(row) {
        lib.request({
            url: "/ccs/endorsement/temporaryStorage",
            data: {
                ids: String(row.id),
            },
            needMask: true,
            success: res => {
                this.load(true);
                message.success("暂存推送成功");
            },
        });
    }

    invalidRow(row) {
        const modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "确定作废该条数据吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/endorsement/discard",
                    method: "POST",
                    data: {
                        id: row.id,
                    },
                    needMask: true,
                    success: res => {
                        if (res) {
                            message.success("作废成功");
                            this.load(true);
                            modal.destroy();
                        }
                    },
                });
            },
        });
    }
    deletedApply(row) {
        const modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            // content: "请确认单一窗口核注单已删除，否则不允许手动触发删除申请",
            content: (
                <>
                    请确认单一窗口核注单已删除，否则不允许手动触发删除申请
                    <br />
                    <a>删除成功将解除关联，清关单可重新建立新核注单</a>
                </>
            ),
            onOk: () => {
                lib.request({
                    url: "/ccs/endorsement/manualDeletedApply",
                    method: "POST",
                    data: { id: row.id },
                    needMask: true,
                    success: res => {
                        if (res) {
                            message.success("手动删除申请成功");
                            this.load(true);
                            modal.destroy();
                        }
                    },
                });
            },
        });
    }
    finishFunc(e, row) {
        const modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "请输入核注单号，手动审核",
            content: (
                <Input
                    onChange={e => {
                        this.setState({
                            realNo: e.target.value,
                        });
                    }}
                />
            ),
            onOk: res => {
                if (this.state.realNo) {
                    lib.request({
                        url: "/ccs/endorsement/finish",
                        data: {
                            id: row.id,
                            realNo: this.state.realNo,
                        },
                        needMask: true,
                        method: "POST",
                        success: res => {
                            if (res.errorMessage) {
                                message.error(res.errorMessage);
                            } else {
                                message.success("审核通过");
                                this.load(true);
                                modal.destroy();
                                this.setState({
                                    realNo: "",
                                });
                            }
                        },
                    });
                } else {
                    message.warning("请输入核注清单编号");
                }
            },
        });
    }

    renderLeftOperation() {
        return (
            <Space>
                {/* <Button onClick={() => this.export()}>批量导出</Button> */}
                <Button type="primary" onClick={() => this.push()}>
                    推送核注
                </Button>
                <Button onClick={() => this.handlePush()}>手动核扣</Button>
                {this.state.buttons.includes("batch-manual-processing") && (
                    <Button onClick={() => this.batchManual()}>批量手动操作</Button>
                )}
                {this.state.buttons.includes("batch-del-apply") && (
                    <Button
                        onClick={() => {
                            this.batchDel();
                        }}>
                        删除申请
                    </Button>
                )}
                {this.state.buttons.includes("supple-pre-entry") && (
                    <Button onClick={() => this.handleReplenish()}>补充预录入编号</Button>
                )}
            </Space>
        );
    }

    batchDel() {
        let { selectedRows } = this.state;
        if (selectedRows.length === 0) {
            message.warning("请选择数据");
            return;
        }
        Modal.confirm({
            title: "删除申请提示",
            content: (
                <>
                    是否确认向海关发起申请删除该核注单？
                    <br />
                    <a>删除成功将解除关联，清关单可重新建立新核注单</a>
                </>
            ),
            onOk: () => {
                lib.request({
                    url: "/ccs/endorsement/deletedApply",
                    data: {
                        ids: selectedRows.map(item => item.id).join(","),
                    },
                    success: () => {
                        this.load();
                        message.success("操作成功");
                    },
                });
            },
        });
    }
    getCheckedRows() {
        let { selectedRows } = this.state;
        return selectedRows;
    }

    handlePush() {
        let ids = "",
            list = this.getCheckedRows();
        if (list.length) {
            let contentStr = "";
            list.map(item => {
                ids += item.id + ",";
                contentStr += `核注清单${item.realEndorsementOrderNo}
`;
            });
            let modal = Modal.confirm({
                cancelText: "取消",
                okText: "确定",
                title: "请确认完成核扣",
                content: <pre>{contentStr}</pre>,
                onOk: () => {
                    lib.request({
                        url: "/ccs/endorsement/handler-check",
                        data: { ids },
                        needMask: true,
                        success: res => {
                            if (res.errorMessage) {
                                message.error(res.errorMessage);
                            } else {
                                message.success("手动核注成功");
                                modal.destroy();
                                this.load(true);
                            }
                        },
                    });
                },
                onCancel: () => {
                    modal.destroy();
                },
            });
        }
    }

    batchManual() {
        let { selectedRows } = this.state;
        if (selectedRows.length === 0) {
            message.warning("请选择数据");
            return;
        }
        this.setState({
            batchManualProcessingModalVisible: true,
        });
    }

    handleReplenish() {
        let { selectedRows } = this.state;
        if (selectedRows.length > 1) {
            message.warning("只能勾选一条数据");
            return;
        }
        if (selectedRows.length === 1) {
            let id = selectedRows[0].id,
                preOrderNo = selectedRows[0].endorsementOrderNo;
            this.setState({
                replenishModalVisible: true,
                editRow: { preOrderNo, id },
            });
        }
    }
    renderRightOperation() {
        return (
            <React.Fragment>
                <Button
                    onClick={() => {
                        this.setState({
                            importModalVisible: true,
                        });
                    }}
                    style={{ marginRight: 8 }}>
                    导入核注清单
                </Button>
                <Button
                    type="primary"
                    onClick={() => {
                        this.setState({
                            visible: true,
                            modalTitle: "新增核注清单",
                        });
                        this.getConfigList();
                    }}>
                    新增核注清单
                </Button>
            </React.Fragment>
        );
    }

    push() {
        let rows = this.getCheckedRows(),
            list = [];
        if (rows.length === 0) {
            message.warning("请选择至少一条数据");
            return;
        }
        rows.map(item => list.push(item.id));
        lib.request({
            url: "/ccs/endorsement/push",
            method: "POST",
            data: {
                idList: list,
            },
            needMask: true,
            success: res => {
                if (!res.errorMessage) {
                    message.success("推送核注成功");
                    this.load(true);
                } else {
                    message.error(res.errorMessage);
                }
            },
        });
    }

    handleOk(values, modalForm) {
        for (let i in values) {
            if (!values[i]) {
                delete values[i];
            }
        }
        if (!values.checklistsFlag) {
            values.checklistsFlag = false;
        }
        if (values.inventoryOrderId) {
            let len = values.inventoryOrderId.indexOf("[");
            values.inventoryOrderId = values.inventoryOrderId.slice(
                0,
                len === -1 ? values.inventoryOrderId.length : len,
            );
        }
        lib.request({
            url: "/ccs/endorsement/createByChecklist",
            data: values,
            method: "POST",
            needMask: true,
            success: res => {
                if (res.errorMessage) {
                    message.error(res.errorMessage);
                } else {
                    this.setState({
                        visible: false,
                    });
                    this.load(true);
                    modalForm && modalForm.resetFields();
                }
            },
        });
    }

    handleCancel() {
        this.setState({
            visible: false,
        });
    }

    tabSubmit(values) {
        this.handleOk(values);
    }

    detailCancel() {
        this.setState({
            editRow: {},
            detailVisible: false,
        });
    }

    detailOk() {
        let recordList = [],
            url = "",
            { mailNo, editRow, type } = this.state;
        if (type === "watch") {
            this.setState({
                detailVisible: false,
            });
            return;
        }
        if (editRow.type === "2") {
            url = "/ccs/endorsement/preEditMailNo";
            if (mailNo) {
                mailNo
                    .split("\n")
                    .filter(item => item && item.trim())
                    .map(item => {
                        recordList.push({
                            mailNo: item,
                        });
                    });
            }
        } else if (editRow.type === "3") {
            url = "/ccs/endorsement/preRefundEditMailNo";
            if (mailNo) {
                mailNo
                    .split("\n")
                    .filter(item => item && item.trim())
                    .map(item => {
                        recordList.push({
                            relNo: item,
                        });
                    });
            }
        }
        lib.request({
            url,
            method: "POST",
            data: {
                id: editRow.id,
                recordList,
            },
            needMask: true,
            success: res => {
                let result = res;
                result.addFailList.map((item, index) => {
                    item.index = index;
                });
                result.addSuccessList.map((item, index) => {
                    item.index = index;
                });
                result.deleteFailList.map((item, index) => {
                    item.index = index;
                });
                result.deleteSuccessList.map((item, index) => {
                    item.index = index;
                });
                this.setState({
                    result,
                    previewVisible: true,
                });
            },
        });
    }

    submitHandler() {
        let { result, editRow } = this.state;
        if (
            (result.addSuccessList === null || result.addSuccessList.length === 0) &&
            (result.deleteSuccessList === null || result.deleteSuccessList.length === 0)
        ) {
            message.warning("暂无有效数据");
            return;
        }
        let url;
        if (editRow.type === "2") {
            url = "/ccs/endorsement/edit/submit";
        } else {
            url = "/ccs/endorsement/submitRefundEditMailNo";
        }
        lib.request({
            url,
            method: "POST",
            data: {
                id: editRow.id,
                addList: result.addSuccessList,
                deleteList: result.deleteSuccessList,
            },
            needMask: true,
            success: res => {
                if (!res.errorMessage) {
                    message.success("修改运单号成功");
                    this.setState({
                        mailNo: "",
                        result: {},
                        previewVisible: false,
                        detailVisible: false,
                    });
                    this.load(true);
                } else {
                    message.error(res.errorMessage);
                }
            },
        });
    }

    tableChange(pagination) {
        this.setState({
            modalPagination: {
                current: pagination.current,
                pageSize: pagination.pageSize,
            },
        });
    }

    renderModal() {
        const {
            previewVisible,
            result,
            detailVisible,
            configList,
            visible,
            editRow,
            type,
            mailNo,
            modalPagination,
            exception,
            eliminateData,
            eliminateModalVisible,
            remackModal,
            replenishModalVisible,
        } = this.state;
        let props = {
            title: this.state.modalTitle,
            onOk: this.handleOk.bind(this),
            onCancel: this.handleCancel.bind(this),
            configList,
            visible,
            form: this.props.form,
            editRow,
            formItemLayout: {
                labelCol: { span: 8 },
                wrapperCol: { span: 14 },
            },
        };
        const columns1 = [
            {
                title: "序号",
                dataIndex: "index",
                render: (text, row, index) => {
                    return 1 + index;
                },
            },
            {
                title: "运单号",
                render: (text, row, index) => {
                    return row.mailNo || row.relNo;
                },
            },
        ];
        const columns2 = [
            {
                title: "序号",
                dataIndex: "index",
                render: (text, row, index) => {
                    return index + 1;
                },
            },
            {
                title: "运单号",
                render: (text, row, index) => {
                    return row.mailNo || row.relNo;
                },
            },
            {
                title: "错误信息",
                dataIndex: "errorMsg",
            },
        ];
        const bodyItemColumns = [
            {
                title: "行号",
                dataIndex: "index",
            },
            {
                title: "商品序号",
                dataIndex: "accountSeqNo",
            },
            {
                title: "料号",
                dataIndex: "productId",
            },
            {
                title: "申报数量",
                dataIndex: "declareQty",
            },
        ];
        const eliminateColumns = [
            {
                title: "商品序号",
                dataIndex: "key",
            },
            {
                title: "料号",
                dataIndex: "value",
            },
        ];
        const eliminateMailColumns = [
            {
                title: "运单号",
                render: (text, record) => record,
            },
        ];
        const remackProps = {
            title: "备注",
            onOk: data => {
                lib.request({
                    url: "/ccs/endorsement/saveRemark",
                    data: {
                        id: editRow.id?.toString(),
                        remark: data.remark,
                    },
                    needMask: true,
                    success: () => {
                        message.success("备注成功");
                        this.load();
                        this.setState({
                            remackModal: false,
                            editRow: {},
                        });
                    },
                });
            },
            onCancel: data => {
                this.setState({
                    remackModal: false,
                    editRow: {},
                });
            },
            configList: [
                {
                    type: "TEXT",
                    labelName: "",
                    render: () => {
                        return (
                            <Alert
                                message={
                                    <>
                                        <ExclamationCircleOutlined
                                            style={{ color: "red", marginLeft: "40px", marginRight: "15px" }}
                                        />
                                        仅用于海关要求核注申报时填写的备注
                                    </>
                                }></Alert>
                        );
                    },
                    wrapperCol: { span: 24 },
                },
                {
                    type: "TEXTAREA",
                    labelName: "备注",
                    labelKey: "remark",
                    required: true,
                    showCount: true,
                    maxLength: 4000,
                    validator: {
                        validator(_, value) {
                            if (!value || value.length <= 4000) return Promise.resolve();
                            return Promise.reject(new Error("不能多于4000个字符"));
                        },
                    },
                },
            ],
            visible: remackModal,
            form: this.remackForm,
            editRow: editRow,
            formItemLayout: {
                labelCol: { span: 6 },
                wrapperCol: { span: 18 },
            },
        };
        let replenishProps = {
            title: "补充预录入编号",
            okText: "保存",
            cancelText: "取消",
            onOk: data => {
                lib.request({
                    url: "/ccs/endorsement/updatePreOrderNo",
                    data: {
                        id: editRow.id.toString(),
                        preOrderNo: data.preOrderNo,
                    },
                    needMask: true,
                    success: () => {
                        message.success("补充成功");
                        this.load();
                        this.setState({
                            replenishModalVisible: false,
                            editRow: {},
                        });
                    },
                });
            },
            onCancel: data => {
                this.setState({
                    replenishModalVisible: false,
                    editRow: {},
                });
            },
            configList: [
                {
                    type: "TEXT",
                    labelName: "",
                    labelCol: { span: 0 },
                    wrapperCol: { span: 20 },
                    render: () => {
                        return (
                            <>
                                <p style={{ color: "#1890ff", marginLeft: "40px", marginBottom: "-10px" }}>
                                    确认已核实填写的预录入编号与海关已生成的预录入编号一致
                                </p>
                            </>
                        );
                    },
                },
                {
                    type: "INPUT",
                    labelName: "预录入编号",
                    labelKey: "preOrderNo",
                    required: true,
                },
            ],
            visible: replenishModalVisible,
            form: this.props.form,
            editRow: editRow,
            formItemLayout: {
                labelCol: { span: 8 },
                wrapperCol: { span: 14 },
            },
        };
        return (
            <React.Fragment>
                {/* 剔除异常结果展示弹窗 */}
                <Modal
                    title="剔除异常"
                    open={eliminateModalVisible}
                    onOk={() => this.setState({ eliminateModalVisible: false, eliminateData: {} })}
                    onCancel={() => this.setState({ eliminateModalVisible: false, eliminateData: {} })}>
                    <Tabs defaultActiveKey="0">
                        <TabPane tab="表体" key="0">
                            <Table
                                dataSource={eliminateData?.exceptionItem}
                                columns={eliminateColumns}
                                pagination={false}
                                scroll={{ y: 400 }}
                            />
                        </TabPane>
                        <TabPane tab="运单号" key="1">
                            <Table
                                dataSource={eliminateData?.exceptionMailNos}
                                columns={eliminateMailColumns}
                                pagination={false}
                                scroll={{ y: 400 }}
                            />
                        </TabPane>
                    </Tabs>
                </Modal>
                <NewModal {...props} />
                <NewModal {...remackProps} />
                <NewModal {...replenishProps} />
                <Modal
                    cancelText="取消"
                    okText="确定"
                    open={detailVisible}
                    width={660}
                    onOk={() => this.detailOk()}
                    onCancel={() => this.detailCancel()}>
                    <Tabs defaultActiveKey="0">
                        <TabPane tab="表体" key="0">
                            {/* 请输入商品账册序号+料号 */}
                            {/* <TextArea value={editRow.defaultStr} disabled rows={20} /> */}
                            <FormItem label="是否异常" wrapperCol={{ span: 8 }}>
                                <Select
                                    value={exception}
                                    onChange={e => {
                                        if (e === "all") {
                                            editRow.bodyList = editRow.bodyItemInfos;
                                        } else {
                                            editRow.bodyList = editRow.bodyItemInfos.filter(
                                                item => item.exception === e,
                                            );
                                        }
                                        this.setState({ editRow, exception: e });
                                    }}>
                                    <Option value={"all"}>全部</Option>
                                    <Option value={true}>异常</Option>
                                    <Option value={false}>正常</Option>
                                </Select>
                            </FormItem>
                            <Table
                                dataSource={editRow.bodyList}
                                columns={bodyItemColumns}
                                rowKey="index"
                                pagination={modalPagination}
                                scroll={{ y: "520px" }}
                                onChange={this.tableChange.bind(this)}
                            />
                        </TabPane>
                        {editRow.type === "1" && (
                            <TabPane tab="单据" key="1">
                                <TextArea disabled value={mailNo} rows={20} />
                            </TabPane>
                        )}
                        {editRow.type === "2" && (
                            <TabPane tab="单据" key="1">
                                请输入运单号(运单数:{mailNo.split("\n").filter(item => !!item.trim()).length})
                                <TextArea
                                    disabled={type === "watch"}
                                    value={mailNo}
                                    rows={20}
                                    onChange={e => {
                                        this.setState({ mailNo: e.target.value });
                                    }}
                                />
                            </TabPane>
                        )}
                        {editRow.type === "3" && (
                            <TabPane tab="单据" key="1">
                                请输入运单号(运单数:{mailNo.split("\n").filter(item => !!item.trim()).length})
                                <TextArea
                                    disabled={type === "watch"}
                                    value={mailNo}
                                    rows={20}
                                    onChange={e => {
                                        this.setState({ mailNo: e.target.value });
                                    }}
                                />
                            </TabPane>
                        )}
                    </Tabs>
                </Modal>
                {previewVisible && (
                    <Modal
                        width="800px"
                        open={previewVisible}
                        title={`提交预览  (修改数量${result && result.totalCount})`}
                        onOk={() => this.submitHandler()}
                        onCancel={() => {
                            this.setState({
                                previewVisible: false,
                                // mailNo: "",
                                // detailVisible: false
                            });
                        }}>
                        <Tabs defaultActiveKey="0">
                            <TabPane tab={`新增成功(${result.addSuccessList && result.addSuccessList.length})`} key="0">
                                <Table dataSource={result.addSuccessList} columns={columns1} rowKey="index"></Table>
                            </TabPane>
                            <TabPane tab={`新增失败(${result.addFailList && result.addFailList.length})`} key="1">
                                <Table dataSource={result.addFailList} columns={columns2} rowKey="index"></Table>
                            </TabPane>
                            <TabPane
                                tab={`删除成功(${result.deleteSuccessList && result.deleteSuccessList.length})`}
                                key="2">
                                <Table dataSource={result.deleteSuccessList} columns={columns1} rowKey="index"></Table>
                            </TabPane>
                            <TabPane tab={`删除失败(${result.deleteFailList && result.deleteFailList.length})`} key="3">
                                <Table dataSource={result.deleteFailList} columns={columns2} rowKey="index"></Table>
                            </TabPane>
                        </Tabs>
                    </Modal>
                )}
                <BatchManualProcessingModal
                    ids={this.state.selectedRows.reduce((prev, curr) => [...prev, curr.id], [])}
                    visible={this.state.batchManualProcessingModalVisible}
                    close={success => {
                        if (success) {
                            this.load();
                        }
                        this.setState({ batchManualProcessingModalVisible: false });
                    }}
                    config={nuclearNoteManageBatchManualData()}
                    submitUrl={"/ccs/endorsement/manualUpdStatus"}
                    type={"Endorsement"}
                />
            </React.Fragment>
        );
    }
}

export default App;
